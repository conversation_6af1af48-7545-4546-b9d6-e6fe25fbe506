using AutoMapper;
using ClosedXML.Excel;
using LinqKit;
using Microsoft.AspNetCore.Mvc;
using Tasin.Website.Common.CommonModels;
using Tasin.Website.Common.CommonModels.BaseModels;
using Tasin.Website.Common.Enums;
using Tasin.Website.Common.Helper;
using Tasin.Website.Common.Services;
using Tasin.Website.Common.Util;
using Tasin.Website.DAL.Interfaces;
using Tasin.Website.DAL.Repository;
using Tasin.Website.DAL.Services.AuthorPredicates;
using Tasin.Website.DAL.Services.WebInterfaces;
using Tasin.Website.Domains.DBContexts;
using Tasin.Website.Domains.Entitites;
using Tasin.Website.Models.SearchModels;
using Tasin.Website.Models.ViewModels;

namespace Tasin.Website.DAL.Services.WebServices
{
    public class PurchaseOrderService : BaseService<PurchaseOrderService>, IPurchaseOrderService
    {
        private readonly IMapper _mapper;
        private IPurchaseOrderRepository _purchaseOrderRepository;
        private IPurchaseOrderItemRepository _purchaseOrderItemRepository;
        private ICustomerRepository _customerRepository;
        private IProductRepository _productRepository;
        private IUnitRepository _unitRepository;

        public PurchaseOrderService(
            ILogger<PurchaseOrderService> logger,
            IConfiguration configuration,
            IHttpContextAccessor httpContextAccessor,
            ICurrentUserContext currentUserContext,
            IUserRepository userRepository,
            IRoleRepository roleRepository,
            SampleDBContext dbContext,
            IMapper mapper,
            IPurchaseOrderRepository purchaseOrderRepository,
            IPurchaseOrderItemRepository purchaseOrderItemRepository,
            ICustomerRepository customerRepository,
            IProductRepository productRepository,
            IUnitRepository unitRepository) : base(logger, configuration, userRepository, roleRepository, httpContextAccessor, currentUserContext, dbContext)
        {
            _mapper = mapper;
            _purchaseOrderRepository = purchaseOrderRepository;
            _purchaseOrderItemRepository = purchaseOrderItemRepository;
            _customerRepository = customerRepository;
            _productRepository = productRepository;
            _unitRepository = unitRepository;
        }

        public async Task<Acknowledgement<JsonResultPaging<List<PurchaseOrderViewModel>>>> GetPurchaseOrderList(PurchaseOrderSearchModel searchModel)
        {
            var ack = new Acknowledgement<JsonResultPaging<List<PurchaseOrderViewModel>>>();
            try
            {
                var predicate = PredicateBuilder.New<Purchase_Order>(true);

                // Only get active purchase orders
                predicate = predicate.And(p => p.IsActive);

                // Apply search filters
                if (!string.IsNullOrWhiteSpace(searchModel.SearchString))
                {
                    var searchValue = searchModel.SearchString.Trim().ToLower();
                    predicate = predicate.And(p => p.Code.ToLower().Contains(searchValue));
                }

                if (searchModel.Customer_ID.HasValue)
                {
                    predicate = predicate.And(p => p.Customer_ID == searchModel.Customer_ID.Value);
                }

                if (!string.IsNullOrWhiteSpace(searchModel.Status))
                {
                    predicate = predicate.And(p => p.Status == searchModel.Status);
                }

                // Add author predicate
                predicate = PurchaseOrderAuthorPredicate.GetPurchaseOrderAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var purchaseOrderQuery = await _purchaseOrderRepository.ReadOnlyRespository.GetWithPagingAsync(
                    filter: predicate,
                    orderBy: q => q.OrderByDescending(u => u.UpdatedDate),
                    paging: new PagingParameters(searchModel.PageNumber, searchModel.PageSize)
                );

                var purchaseOrderViewModels = _mapper.Map<List<PurchaseOrderViewModel>>(purchaseOrderQuery.Data);

                if (purchaseOrderViewModels.Any())
                {
                    // OPTIMIZED: Get all required data in batch
                    var customerIds = purchaseOrderViewModels.Select(p => p.Customer_ID).Distinct().ToList();
                    var purchaseOrderIds = purchaseOrderViewModels.Select(p => p.Id).ToList();

                    // OPTIMIZED: Sequential execution to avoid DbContext threading issues
                    var customers = await _customerRepository.ReadOnlyRespository.GetAsync(
                        filter: c => customerIds.Contains(c.ID)
                    );

                    var allPurchaseOrderItems = await _purchaseOrderItemRepository.ReadOnlyRespository.GetAsync(
                        filter: poi => purchaseOrderIds.Contains(poi.PO_ID)
                    );

                    var purchaseOrderItemViewModels = _mapper.Map<List<PurchaseOrderItemViewModel>>(allPurchaseOrderItems);

                    // OPTIMIZED: Create lookup dictionaries for O(1) access
                    var customerLookup = customers.ToDictionary(c => c.ID, c => c);
                    var itemsByPOId = purchaseOrderItemViewModels.GroupBy(poi => poi.PO_ID).ToDictionary(g => g.Key, g => g.ToList());

                    // Get product and unit names for all items (only if there are items)
                    Dictionary<int, Product> productLookup = new Dictionary<int, Product>();
                    Dictionary<int, Unit> unitLookup = new Dictionary<int, Unit>();

                    if (purchaseOrderItemViewModels.Any())
                    {
                        var productIds = purchaseOrderItemViewModels.Select(p => p.Product_ID).Distinct().ToList();
                        var unitIds = purchaseOrderItemViewModels.Where(p => p.Unit_ID.HasValue).Select(p => p.Unit_ID!.Value).Distinct().ToList();

                        // OPTIMIZED: Sequential execution for products and units
                        if (productIds.Any())
                        {
                            var products = await _productRepository.ReadOnlyRespository.GetAsync(
                                filter: p => productIds.Contains(p.ID)
                            );
                            productLookup = products.ToDictionary(p => p.ID, p => p);
                        }

                        if (unitIds.Any())
                        {
                            var units = await _unitRepository.ReadOnlyRespository.GetAsync(
                                filter: u => unitIds.Contains(u.ID)
                            );
                            unitLookup = units.ToDictionary(u => u.ID, u => u);
                        }

                        // OPTIMIZED: Single loop to populate product and unit names using lookups
                        foreach (var item in purchaseOrderItemViewModels)
                        {
                            if (productLookup.TryGetValue(item.Product_ID, out var product))
                            {
                                item.ProductName = product.Name;
                            }

                            if (item.Unit_ID.HasValue && unitLookup.TryGetValue(item.Unit_ID.Value, out var unit))
                            {
                                item.UnitName = unit.Name;
                            }

                            // ProcessingTypeName is now computed from enum in ViewModel
                        }
                    }

                    // OPTIMIZED: Single loop to populate customer names and items using lookups
                    foreach (var purchaseOrder in purchaseOrderViewModels)
                    {
                        if (customerLookup.TryGetValue(purchaseOrder.Customer_ID, out var customer))
                        {
                            purchaseOrder.CustomerName = customer.Name;
                        }

                        // OPTIMIZED: Use lookup dictionary instead of Where query
                        purchaseOrder.PurchaseOrderItems = itemsByPOId.TryGetValue(purchaseOrder.Id, out var items) ? items : new List<PurchaseOrderItemViewModel>();
                    }
                }

                var result = new JsonResultPaging<List<PurchaseOrderViewModel>>
                {
                    Data = purchaseOrderViewModels,
                    PageNumber = searchModel.PageNumber,
                    PageSize = searchModel.PageSize,
                    Total = purchaseOrderQuery.TotalRecords
                };

                ack.IsSuccess = true;
                ack.Data = result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetPurchaseOrderList: {ex.Message}");
                ack.AddMessage(ex.Message);
            }

            return ack;
        }

        public async Task<Acknowledgement<PurchaseOrderViewModel>> GetPurchaseOrderById(int purchaseOrderId)
        {
            var ack = new Acknowledgement<PurchaseOrderViewModel>();
            try
            {
                var purchaseOrder = await _purchaseOrderRepository.ReadOnlyRespository.FindAsync(purchaseOrderId);
                if (purchaseOrder == null)
                {
                    ack.AddMessage("Không tìm thấy đơn hàng.");
                    return ack;
                }

                // Check author predicate
                var predicate = PredicateBuilder.New<Purchase_Order>(true);
                predicate = predicate.And(p => p.ID == purchaseOrderId);
                predicate = PurchaseOrderAuthorPredicate.GetPurchaseOrderAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var authorizedPurchaseOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                    filter: predicate
                );

                if (authorizedPurchaseOrders.Count == 0)
                {
                    ack.AddMessage("Bạn không có quyền xem đơn hàng này.");
                    return ack;
                }

                var purchaseOrderViewModel = _mapper.Map<PurchaseOrderViewModel>(purchaseOrder);

                // OPTIMIZED: Get purchase order items first to determine what other data we need
                var purchaseOrderItems = await _purchaseOrderItemRepository.GetByPurchaseOrderIdAsync(purchaseOrderId);
                var purchaseOrderItemViewModels = _mapper.Map<List<PurchaseOrderItemViewModel>>(purchaseOrderItems);

                // OPTIMIZED: Sequential execution to avoid DbContext threading issues
                var customer = await _customerRepository.ReadOnlyRespository.FindAsync(purchaseOrder.Customer_ID);
                if (customer != null)
                {
                    purchaseOrderViewModel.CustomerName = customer.Name;
                }

                if (purchaseOrderItemViewModels.Any())
                {
                    // OPTIMIZED: Collect all IDs needed for batch queries
                    var productIds = purchaseOrderItemViewModels.Select(p => p.Product_ID).Distinct().ToList();
                    var unitIds = purchaseOrderItemViewModels.Where(p => p.Unit_ID.HasValue).Select(p => p.Unit_ID!.Value).Distinct().ToList();

                    // OPTIMIZED: Sequential execution for products and units
                    var products = await _productRepository.ReadOnlyRespository.GetAsync(
                        filter: p => productIds.Contains(p.ID)
                    );

                    var units = unitIds.Any() ? await _unitRepository.ReadOnlyRespository.GetAsync(
                        filter: u => unitIds.Contains(u.ID)
                    ) : new List<Unit>();

                    // OPTIMIZED: Create lookup dictionaries for O(1) access
                    var productLookup = products.ToDictionary(p => p.ID, p => p);
                    var unitLookup = units.ToDictionary(u => u.ID, u => u);

                    // OPTIMIZED: Single loop to populate product and unit names using lookups
                    foreach (var item in purchaseOrderItemViewModels)
                    {
                        if (productLookup.TryGetValue(item.Product_ID, out var product))
                        {
                            item.ProductName = product.Name;
                        }

                        if (item.Unit_ID.HasValue && unitLookup.TryGetValue(item.Unit_ID.Value, out var unit))
                        {
                            item.UnitName = unit.Name;
                        }

                        // ProcessingTypeName is now computed from enum in ViewModel
                    }
                }

                purchaseOrderViewModel.PurchaseOrderItems = purchaseOrderItemViewModels;

                ack.IsSuccess = true;
                ack.Data = purchaseOrderViewModel;
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetPurchaseOrderById: {ex.Message}");
                ack.AddMessage(ex.Message);
            }

            return ack;
        }

        public async Task<Acknowledgement> CreateOrUpdatePurchaseOrder(PurchaseOrderViewModel postData)
        {
            var ack = new Acknowledgement();

            // Input validation
            if (postData == null)
            {
                ack.AddMessage("Dữ liệu đơn hàng không hợp lệ.");
                return ack;
            }

            if (postData.Customer_ID <= 0)
            {
                ack.AddMessage("Vui lòng chọn khách hàng.");
                return ack;
            }

            if (postData.PurchaseOrderItems == null || !postData.PurchaseOrderItems.Any())
            {
                ack.AddMessage("Đơn hàng phải có ít nhất một sản phẩm.");
                return ack;
            }

            // Validate purchase order items
            foreach (var item in postData.PurchaseOrderItems)
            {
                if (item.Product_ID <= 0)
                {
                    ack.AddMessage("Sản phẩm không hợp lệ.");
                    return ack;
                }

                if (item.Quantity <= 0)
                {
                    ack.AddMessage("Số lượng sản phẩm phải lớn hơn 0.");
                    return ack;
                }
            }

            // Use transaction to ensure data consistency
            using var transaction = await DbContext.Database.BeginTransactionAsync();
            try
            {
                if (postData.Id == 0)
                {
                    // Create new purchase order
                    var newPurchaseOrder = _mapper.Map<Purchase_Order>(postData);
                    newPurchaseOrder.Code = await Generator.GenerateEntityCodeAsync(EntityPrefix.PurchaseOrder, DbContext);
                    newPurchaseOrder.CreatedDate = DateTime.Now;
                    newPurchaseOrder.CreatedBy = CurrentUserId;
                    newPurchaseOrder.UpdatedDate = newPurchaseOrder.CreatedDate;
                    newPurchaseOrder.UpdatedBy = newPurchaseOrder.CreatedBy;

                    // Calculate totals
                    CalculateTotals(newPurchaseOrder, postData.PurchaseOrderItems);

                    await _purchaseOrderRepository.Repository.AddWithoutSaveAsync(newPurchaseOrder);
                    await DbContext.SaveChangesAsync();

                    if (postData.PurchaseOrderItems.Count > 0)
                    {
                        await SavePurchaseOrderItemsInTransaction(newPurchaseOrder.ID, postData.PurchaseOrderItems);
                        await DbContext.SaveChangesAsync();
                    }

                    ack.IsSuccess = true;
                }
                else
                {
                    // Update existing purchase order
                    var existingPurchaseOrder = await _purchaseOrderRepository.Repository.FindAsync(postData.Id);
                    if (existingPurchaseOrder == null)
                    {
                        ack.AddMessage("Không tìm thấy đơn hàng.");
                        await transaction.RollbackAsync();
                        return ack;
                    }

                    // Check author predicate
                    var predicate = PredicateBuilder.New<Purchase_Order>(true);
                    predicate = predicate.And(p => p.ID == postData.Id);
                    predicate = PurchaseOrderAuthorPredicate.GetPurchaseOrderAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                    var authorizedPurchaseOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                        filter: predicate
                    );

                    if (authorizedPurchaseOrders.Count == 0)
                    {
                        ack.AddMessage("Bạn không có quyền cập nhật đơn hàng này.");
                        await transaction.RollbackAsync();
                        return ack;
                    }

                    // Allow editing for New status only
                    if (existingPurchaseOrder.Status != EPOStatus.New.ToString())
                    {
                        if (existingPurchaseOrder.Status == EPOStatus.Executed.ToString())
                        {
                            ack.AddMessage("Không thể chỉnh sửa đơn hàng đã được tạo đơn tổng hợp.");
                        }
                        else if (existingPurchaseOrder.Status == EPOStatus.Cancel.ToString())
                        {
                            ack.AddMessage("Không thể chỉnh sửa đơn hàng đã bị hủy.");
                        }
                        else
                        {
                            ack.AddMessage("Đơn hàng không thể chỉnh sửa (Chỉ có thể chỉnh sửa đơn hàng ở trạng thái: Mới).");
                        }
                        await transaction.RollbackAsync();
                        return ack;
                    }

                    existingPurchaseOrder.Customer_ID = postData.Customer_ID;
                    existingPurchaseOrder.Status = postData.Status.ToString();
                    existingPurchaseOrder.UpdatedDate = DateTime.Now;
                    existingPurchaseOrder.UpdatedBy = CurrentUserId;

                    // Calculate totals
                    CalculateTotals(existingPurchaseOrder, postData.PurchaseOrderItems);

                    // Update purchase order (without saving)
                    _purchaseOrderRepository.Repository.UpdateWithoutSave(existingPurchaseOrder);

                    if (postData.PurchaseOrderItems.Count > 0)
                    {
                        await DeletePurchaseOrderItemsInTransaction(postData.Id);
                        await SavePurchaseOrderItemsInTransaction(existingPurchaseOrder.ID, postData.PurchaseOrderItems);
                    }

                    // Save all changes within transaction
                    await DbContext.SaveChangesAsync();
                    ack.IsSuccess = true;
                }

                // Commit transaction if everything succeeded
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError("CreateOrUpdatePurchaseOrder failed: {ErrorMessage}", ex.Message);
                ack.AddMessage(ex.Message);
                ack.IsSuccess = false;

                // Rollback transaction on error
                try
                {
                    await transaction.RollbackAsync();
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError("Transaction rollback failed: {ErrorMessage}", rollbackEx.Message);
                }
            }

            return ack;
        }

        private static void CalculateTotals(Purchase_Order purchaseOrder, List<PurchaseOrderItemViewModel> items)
        {
            decimal totalPrice = 0;
            decimal totalPriceNoTax = 0;

            foreach (var item in items)
            {
                if (item.Price.HasValue)
                {
                    decimal baseAmount = item.Quantity * item.Price.Value;
                    // Removed lossAmount from calculation as per requirement
                    decimal totalProcessingFee = item.Quantity * (item.ProcessingFee ?? 0);
                    decimal totalBeforeTax = baseAmount + (item.AdditionalCost ?? 0) + totalProcessingFee;
                    decimal taxAmount = totalBeforeTax * ((item.TaxRate ?? 0) / 100);

                    totalPriceNoTax += totalBeforeTax;
                    totalPrice += totalBeforeTax + taxAmount;
                }
            }

            purchaseOrder.TotalPrice = totalPrice;
            purchaseOrder.TotalPriceNoTax = totalPriceNoTax;
        }

        /// <summary>
        /// Save purchase order items within a transaction (does not call SaveChanges)
        /// </summary>
        private async Task SavePurchaseOrderItemsInTransaction(int purchaseOrderId, List<PurchaseOrderItemViewModel> items)
        {
            if (items == null || items.Count == 0)
                return;

            var purchaseOrderItems = new List<Purchase_Order_Item>();

            foreach (var item in items)
            {
                var purchaseOrderItem = new Purchase_Order_Item
                {
                    PO_ID = purchaseOrderId,
                    Product_ID = item.Product_ID,
                    Quantity = item.Quantity,
                    Unit_ID = item.Unit_ID,
                    Price = item.Price,
                    TaxRate = item.TaxRate,
                    LossRate = item.LossRate,
                    AdditionalCost = item.AdditionalCost,
                    ProcessingFee = item.ProcessingFee,
                    Note = item.Note
                };

                purchaseOrderItems.Add(purchaseOrderItem);
            }

            // Add items to context without saving (transaction will handle the save)
            await _purchaseOrderItemRepository.Repository.AddRangeWithoutSaveAsync(purchaseOrderItems);
        }

        /// <summary>
        /// Delete purchase order items within a transaction (does not call SaveChanges)
        /// </summary>
        private async Task DeletePurchaseOrderItemsInTransaction(int purchaseOrderId)
        {
            var existingItems = await _purchaseOrderItemRepository.ReadOnlyRespository.GetAsync(
                filter: item => item.PO_ID == purchaseOrderId
            );

            if (existingItems.Count > 0)
            {
                _purchaseOrderItemRepository.Repository.DeleteRangeWithoutSave(existingItems);
            }
        }

        public async Task<Acknowledgement> DeletePurchaseOrderById(int purchaseOrderId)
        {
            var ack = new Acknowledgement();
            try
            {
                var purchaseOrder = await _purchaseOrderRepository.Repository.FindAsync(purchaseOrderId);
                if (purchaseOrder == null)
                {
                    ack.AddMessage("Không tìm thấy đơn hàng.");
                    return ack;
                }

                // Check author predicate
                var predicate = PredicateBuilder.New<Purchase_Order>(true);
                predicate = predicate.And(p => p.ID == purchaseOrderId);
                predicate = PurchaseOrderAuthorPredicate.GetPurchaseOrderAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var authorizedPurchaseOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                    filter: predicate
                );

                if (authorizedPurchaseOrders.Count == 0)
                {
                    ack.AddMessage("Bạn không có quyền xóa đơn hàng này.");
                    return ack;
                }

                // Allow deletion for New status only
                if (purchaseOrder.Status != EPOStatus.New.ToString())
                {
                    if (purchaseOrder.Status == EPOStatus.Executed.ToString())
                    {
                        ack.AddMessage("Không thể xóa đơn hàng đã được tạo đơn tổng hợp.");
                    }
                    else if (purchaseOrder.Status == EPOStatus.Cancel.ToString())
                    {
                        ack.AddMessage("Không thể xóa đơn hàng đã bị hủy.");
                    }
                    else
                    {
                        ack.AddMessage("Đơn hàng không thể xóa (Chỉ có thể xóa đơn hàng ở trạng thái: Mới).");
                    }
                    return ack;
                }

                // Set purchase order as inactive instead of deleting
                purchaseOrder.IsActive = false;
                purchaseOrder.UpdatedDate = DateTime.Now;
                purchaseOrder.UpdatedBy = CurrentUserId;

                await ack.TrySaveChangesAsync(res => res.UpdateAsync(purchaseOrder), _purchaseOrderRepository.Repository);
            }
            catch (Exception ex)
            {
                _logger.LogError("DeletePurchaseOrderById failed: {ErrorMessage}", ex.Message);
                ack.AddMessage(ex.Message);
            }

            return ack;
        }

        public async Task<Acknowledgement> CancelPurchaseOrderById(int purchaseOrderId)
        {
            var ack = new Acknowledgement();
            try
            {
                var purchaseOrder = await _purchaseOrderRepository.Repository.FindAsync(purchaseOrderId);
                if (purchaseOrder == null)
                {
                    ack.AddMessage("Không tìm thấy đơn hàng.");
                    return ack;
                }

                // Check author predicate
                var predicate = PredicateBuilder.New<Purchase_Order>(true);
                predicate = predicate.And(p => p.ID == purchaseOrderId);
                predicate = PurchaseOrderAuthorPredicate.GetPurchaseOrderAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var authorizedPurchaseOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                    filter: predicate
                );

                if (authorizedPurchaseOrders.Count == 0)
                {
                    ack.AddMessage("Bạn không có quyền hủy đơn hàng này.");
                    return ack;
                }

                // Check if order can be cancelled - only allow cancellation for New and Confirmed statuses
                var allowedCancelStatuses = new[] {
                    EPOStatus.New.ToString(),
                    EPOStatus.Confirmed.ToString()
                };

                if (!allowedCancelStatuses.Contains(purchaseOrder.Status))
                {
                    if (purchaseOrder.Status == EPOStatus.Cancel.ToString())
                    {
                        ack.AddMessage("Đơn hàng đã được hủy trước đó.");
                    }
                    else if (purchaseOrder.Status == EPOStatus.Executed.ToString())
                    {
                        ack.AddMessage("Không thể hủy đơn hàng đã được tạo đơn tổng hợp.");
                    }
                    else
                    {
                        ack.AddMessage("Đơn hàng không thể hủy (Chỉ có thể hủy đơn hàng ở trạng thái: Mới, Đã xác nhận).");
                    }
                    return ack;
                }

                // Update status to Cancel
                purchaseOrder.Status = EPOStatus.Cancel.ToString();
                purchaseOrder.UpdatedDate = DateTime.Now;
                purchaseOrder.UpdatedBy = CurrentUserId;

                await ack.TrySaveChangesAsync(res => res.UpdateAsync(purchaseOrder), _purchaseOrderRepository.Repository);
            }
            catch (Exception ex)
            {
                _logger.LogError("CancelPurchaseOrderById failed: {ErrorMessage}", ex.Message);
                ack.AddMessage(ex.Message);
            }

            return ack;
        }

        public async Task<Acknowledgement<int>> CountConfirmedPurchaseOrders()
        {
            var ack = new Acknowledgement<int>();
            try
            {
                var predicate = PredicateBuilder.New<Purchase_Order>(true);

                // Only get active purchase orders
                predicate = predicate.And(p => p.IsActive);

                // Only get confirmed purchase orders
                predicate = predicate.And(p => p.Status == EPOStatus.Confirmed.ToString());

                // Apply author predicate for user authorization
                predicate = PurchaseOrderAuthorPredicate.GetPurchaseOrderAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                // Get the list of confirmed purchase orders and count them
                var confirmedOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                    filter: predicate
                );

                ack.Data = confirmedOrders.Count;
                ack.IsSuccess = true;
            }
            catch (Exception ex)
            {
                _logger.LogError("CountConfirmedPurchaseOrders failed: {ErrorMessage}", ex.Message);
                ack.AddMessage(ex.Message);
            }

            return ack;
        }

        /// <summary>
        /// Export Purchase Order as Excel with invoice format
        /// </summary>
        /// <param name="purchaseOrderId">Purchase Order ID</param>
        /// <returns>Excel file as byte array</returns>
        public async Task<byte[]> ExportPurchaseOrderAsExcel(int purchaseOrderId)
        {
            try
            {
                var purchaseOrderIds = new List<int> { purchaseOrderId };
                return await GeneratePurchaseOrderExcel(purchaseOrderIds, "Hóa đơn");
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportPurchaseOrderAsExcel: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Export multiple Purchase Orders as Excel with invoice format
        /// </summary>
        /// <param name="purchaseOrderIds">List of Purchase Order IDs</param>
        /// <returns>Excel file as byte array</returns>
        public async Task<byte[]> BulkExportPurchaseOrdersAsExcel(List<int> purchaseOrderIds)
        {
            try
            {
                if (purchaseOrderIds == null || !purchaseOrderIds.Any())
                {
                    throw new Exception("Danh sách đơn hàng không được để trống");
                }

                return await GeneratePurchaseOrderExcel(purchaseOrderIds, "Hóa đơn tổng hợp");
            }
            catch (Exception ex)
            {
                _logger.LogError($"BulkExportPurchaseOrdersAsExcel: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Shared method to generate Excel for Purchase Orders
        /// </summary>
        /// <param name="purchaseOrderIds">List of Purchase Order IDs</param>
        /// <param name="worksheetName">Name of the Excel worksheet</param>
        /// <returns>Excel file as byte array</returns>
        private async Task<byte[]> GeneratePurchaseOrderExcel(List<int> purchaseOrderIds, string worksheetName)
        {
            // Get purchase orders with all related data
            var purchaseOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                filter: p => purchaseOrderIds.Contains(p.ID),
                includeProperties: "Customer"
            );

            if (!purchaseOrders.Any())
            {
                throw new Exception("Không tìm thấy đơn hàng nào");
            }

            // Check authorization for all orders
            var predicate = PredicateBuilder.New<Purchase_Order>(true);
            predicate = predicate.And(p => purchaseOrderIds.Contains(p.ID));
            predicate = PurchaseOrderAuthorPredicate.GetPurchaseOrderAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

            var authorizedPOs = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(filter: predicate);
            if (authorizedPOs.Count() != purchaseOrderIds.Count)
            {
                throw new Exception("Không có quyền truy cập một số đơn hàng được chọn");
            }

            using (var workbook = ExcelHelper.CreateWorkbook())
            {
                var worksheet = workbook.Worksheets.Add(worksheetName);

                // Set up headers - extracted to method for reusability
                SetupExcelHeaders(worksheet);

                int row = 2;

                // Process each purchase order
                foreach (var po in purchaseOrders.OrderBy(p => p.Code))
                {
                    int sequentialNumber = 1;
                    // Get purchase order items with related data
                    var poItems = await _purchaseOrderItemRepository.ReadOnlyRespository.GetAsync(
                        filter: poi => poi.PO_ID == po.ID,
                        includeProperties: "Product,Unit"
                    );

                    // Add data rows for each purchase order item
                    foreach (var item in poItems)
                    {
                        PopulateExcelRow(worksheet, row, po, item, sequentialNumber);
                        row++;
                        sequentialNumber++;
                    }
                }

                // Auto-fit columns
                worksheet.Columns().AdjustToContents();

                return ExcelHelper.SaveToByteArray(workbook);
            }
        }

        /// <summary>
        /// Setup Excel headers for Purchase Order export
        /// </summary>
        /// <param name="worksheet">Excel worksheet</param>
        private static void SetupExcelHeaders(IXLWorksheet worksheet)
        {
            var headers = new[]
            {
                "MaHD", "NgayHoaDon", "MaKhachHang", "TenNguoiMua", "TenDonVi", "MaSoThue",
                "DiaChiKhachHang", "SoDienThoai", "SoBangKe", "NgayBangKe", "SOTKKHACH",
                "TENNHKHACH", "HinhThucThanhToan", "ThueSuat", "ThueSuatKhac", "MaHang",
                "TenHangHoa", "DVT", "SoLuong", "DonGia", "ThanhTien", "TienTe", "SoTT",
                "TinhChat", "Email", "Ghichu", "TyGia", "GiamTruHoaDon", "GiamTruTungDongHangHoa",
                "TienGiamTru", "TyLe%ChietKhau", "TienChietKhau", "TienThue"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                var cell = worksheet.Cell(1, i + 1);
                ExcelHelper.SetCellValue(cell, headers[i]);
                cell.Style.Font.Bold = true;
                cell.Style.Fill.BackgroundColor = XLColor.LightBlue;
                cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            }
        }

        /// <summary>
        /// Populate a single Excel row with Purchase Order item data
        /// </summary>
        /// <param name="worksheet">Excel worksheet</param>
        /// <param name="row">Row number</param>
        /// <param name="po">Purchase Order</param>
        /// <param name="item">Purchase Order Item</param>
        /// <param name="sequentialNumber">Sequential number for SoTT</param>
        private static void PopulateExcelRow(IXLWorksheet worksheet, int row, Purchase_Order po, Purchase_Order_Item item, int sequentialNumber)
        {
            // Calculate line total: (Price + ProcessingFee) * Quantity + AdditionalCost
            var lineTotal = ((item.Price ?? 0) + (item.ProcessingFee ?? 0)) * item.Quantity + (item.AdditionalCost ?? 0);
            var taxAmount = lineTotal * ((item.TaxRate ?? 0) / 100);

            // Column mapping
            ExcelHelper.SetCellValue(worksheet.Cell(row, 1), po.Code); // MaHD
            ExcelHelper.SetCellValue(worksheet.Cell(row, 2), po.CreatedDate.ToString("dd/MM/yyyy")); // NgayHoaDon
            ExcelHelper.SetCellValue(worksheet.Cell(row, 3), po.Customer?.Code ?? ""); // MaKhachHang
            ExcelHelper.SetCellValue(worksheet.Cell(row, 4), po.Customer?.Name ?? ""); // TenNguoiMua
            ExcelHelper.SetCellValue(worksheet.Cell(row, 5), po.Customer?.Name ?? ""); // TenDonVi
            ExcelHelper.SetCellValue(worksheet.Cell(row, 6), po.Customer?.TaxCode ?? ""); // MaSoThue
            ExcelHelper.SetCellValue(worksheet.Cell(row, 7), po.Customer?.Address ?? ""); // DiaChiKhachHang
            ExcelHelper.SetCellValue(worksheet.Cell(row, 8), po.Customer?.PhoneContact ?? ""); // SoDienThoai
            ExcelHelper.SetCellValue(worksheet.Cell(row, 9), ""); // SoBangKe - empty
            ExcelHelper.SetCellValue(worksheet.Cell(row, 10), ""); // NgayBangKe - empty
            ExcelHelper.SetCellValue(worksheet.Cell(row, 11), po.CustomerAccountNumber ?? ""); // SOTKKHACH
            ExcelHelper.SetCellValue(worksheet.Cell(row, 12), po.CustomerBankName ?? ""); // TENNHKHACH
            ExcelHelper.SetCellValue(worksheet.Cell(row, 13), po.PaymentMethod.HasValue ? EnumHelper.GetEnumDescriptionByEnum(po.PaymentMethod.Value) : ""); // HinhThucThanhToan
            worksheet.Cell(row, 14).Value = item.TaxRate ?? 0; // ThueSuat
            ExcelHelper.SetCellValue(worksheet.Cell(row, 15), ""); // ThueSuatKhac - empty
            ExcelHelper.SetCellValue(worksheet.Cell(row, 16), item.Product?.Code ?? ""); // MaHang
            ExcelHelper.SetCellValue(worksheet.Cell(row, 17), item.Product?.Name ?? ""); // TenHangHoa
            ExcelHelper.SetCellValue(worksheet.Cell(row, 18), item.Unit?.Name ?? ""); // DVT
            worksheet.Cell(row, 19).Value = item.Quantity; // SoLuong
            worksheet.Cell(row, 20).Value = item.Price ?? 0; // DonGia
            worksheet.Cell(row, 21).Value = lineTotal; // ThanhTien
            ExcelHelper.SetCellValue(worksheet.Cell(row, 22), "VNĐ"); // TienTe
            worksheet.Cell(row, 23).Value = sequentialNumber; // SoTT
            worksheet.Cell(row, 24).Value = 1; // TinhChat - default to 1
            ExcelHelper.SetCellValue(worksheet.Cell(row, 25), po.Customer?.Email ?? ""); // Email
            ExcelHelper.SetCellValue(worksheet.Cell(row, 26), item.Note ?? ""); // Ghichu
            ExcelHelper.SetCellValue(worksheet.Cell(row, 27), ""); // TyGia - empty
            ExcelHelper.SetCellValue(worksheet.Cell(row, 28), ""); // GiamTruHoaDon - empty
            ExcelHelper.SetCellValue(worksheet.Cell(row, 29), ""); // GiamTruTungDongHangHoa - empty
            ExcelHelper.SetCellValue(worksheet.Cell(row, 30), ""); // TienGiamTru - empty
            ExcelHelper.SetCellValue(worksheet.Cell(row, 31), ""); // TyLe%ChietKhau - empty
            ExcelHelper.SetCellValue(worksheet.Cell(row, 32), ""); // TienChietKhau - empty
            worksheet.Cell(row, 33).Value = taxAmount; // TienThue

            // Format numeric columns
            worksheet.Cell(row, 14).Style.NumberFormat.Format = "#,##0.00"; // ThueSuat
            worksheet.Cell(row, 19).Style.NumberFormat.Format = "#,##0.00"; // SoLuong
            worksheet.Cell(row, 20).Style.NumberFormat.Format = "#,##0"; // DonGia
            worksheet.Cell(row, 21).Style.NumberFormat.Format = "#,##0"; // ThanhTien
            worksheet.Cell(row, 33).Style.NumberFormat.Format = "#,##0"; // TienThue
        }

        public new void Dispose()
        {
            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
